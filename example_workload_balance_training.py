#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
example_workload_balance_training.py

Example script demonstrating how to use the workload balance functionality
in lr_scheduler_train.py for multi-objective optimization.
"""

import subprocess
import sys
import os

def run_training_example():
    """Run an example training with workload balance enabled."""
    
    print("Multi-Objective Training Example")
    print("=" * 50)
    print()
    
    # Example 1: Single-objective training (makespan only)
    print("Example 1: Single-objective training (makespan only)")
    print("-" * 50)
    
    cmd_single = [
        sys.executable, "lr_scheduler_train.py",
        "--path-to-train", "./gen/r2t20_001",
        "--num-robots", "2",
        "--train-start-no", "1",
        "--train-end-no", "10",
        "--steps", "100",
        "--batch-size", "4",
        "--lr", "1e-4",
        "--checkpoint-interval", "50",
        "--cpsave", "./cp_single_objective"
    ]
    
    print("Command:")
    print(" ".join(cmd_single))
    print()
    print("This will train with makespan optimization only (traditional approach)")
    print()
    
    # Example 2: Multi-objective training with workload balance
    print("Example 2: Multi-objective training with workload balance")
    print("-" * 50)
    
    cmd_multi = [
        sys.executable, "lr_scheduler_train.py",
        "--path-to-train", "./gen/r2t20_001",
        "--num-robots", "2",
        "--train-start-no", "1",
        "--train-end-no", "10",
        "--steps", "100",
        "--batch-size", "4",
        "--lr", "1e-4",
        "--alpha", "0.7",  # 70% weight for makespan
        "--beta", "0.3",   # 30% weight for workload balance
        "--enable-workload-balance",
        "--checkpoint-interval", "50",
        "--cpsave", "./cp_multi_objective"
    ]
    
    print("Command:")
    print(" ".join(cmd_multi))
    print()
    print("This will train with both makespan and workload balance optimization")
    print("- Alpha (0.7): Weight for makespan objective")
    print("- Beta (0.3): Weight for workload balance objective")
    print()
    
    # Example 3: Workload balance focused training
    print("Example 3: Workload balance focused training")
    print("-" * 50)
    
    cmd_balance = [
        sys.executable, "lr_scheduler_train.py",
        "--path-to-train", "./gen/r2t20_001",
        "--num-robots", "2",
        "--train-start-no", "1",
        "--train-end-no", "10",
        "--steps", "100",
        "--batch-size", "4",
        "--lr", "1e-4",
        "--alpha", "0.3",  # 30% weight for makespan
        "--beta", "0.7",   # 70% weight for workload balance
        "--enable-workload-balance",
        "--checkpoint-interval", "50",
        "--cpsave", "./cp_balance_focused"
    ]
    
    print("Command:")
    print(" ".join(cmd_balance))
    print()
    print("This will train with emphasis on workload balance")
    print("- Alpha (0.3): Weight for makespan objective")
    print("- Beta (0.7): Weight for workload balance objective")
    print()
    
    print("Key Features Added:")
    print("-" * 20)
    print("1. --alpha: Weight for makespan objective (0-1)")
    print("2. --beta: Weight for workload balance objective (0-1)")
    print("3. --enable-workload-balance: Enable multi-objective optimization")
    print()
    print("Notes:")
    print("- Alpha + Beta should typically sum to 1.0 for balanced weighting")
    print("- Higher alpha emphasizes makespan minimization")
    print("- Higher beta emphasizes workload balance")
    print("- Workload balance is measured as standard deviation of task counts per robot")
    print("- Lower workload balance values indicate more balanced task distribution")
    print()
    
    # Ask user if they want to run one of the examples
    print("Would you like to run one of these examples? (y/n): ", end="")
    try:
        response = input().strip().lower()
        if response == 'y' or response == 'yes':
            print()
            print("Which example would you like to run?")
            print("1. Single-objective (makespan only)")
            print("2. Multi-objective (balanced)")
            print("3. Workload balance focused")
            print("Enter choice (1-3): ", end="")
            
            choice = input().strip()
            
            if choice == "1":
                print("\nRunning single-objective training...")
                subprocess.run(cmd_single)
            elif choice == "2":
                print("\nRunning multi-objective training...")
                subprocess.run(cmd_multi)
            elif choice == "3":
                print("\nRunning workload balance focused training...")
                subprocess.run(cmd_balance)
            else:
                print("Invalid choice. Exiting.")
        else:
            print("Examples not executed. Commands shown above for reference.")
    except KeyboardInterrupt:
        print("\nExiting...")
    except Exception as e:
        print(f"\nError: {e}")

def show_checkpoint_info():
    """Show information about saved checkpoints."""
    print("\nCheckpoint Information:")
    print("-" * 30)
    print("The enhanced lr_scheduler_train.py now saves additional information in checkpoints:")
    print("- alpha: Weight for makespan objective")
    print("- beta: Weight for workload balance objective") 
    print("- enable_workload_balance: Whether workload balance was enabled")
    print()
    print("This allows you to:")
    print("1. Resume training with the same multi-objective settings")
    print("2. Analyze which objective weights were used for a model")
    print("3. Compare models trained with different objective balances")

if __name__ == "__main__":
    run_training_example()
    show_checkpoint_info()
