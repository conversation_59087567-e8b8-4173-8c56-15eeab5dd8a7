# Workload Balance Integration for lr_scheduler_train.py

## Overview

This document describes the integration of workload balance functionality into `lr_scheduler_train.py`, enabling multi-objective optimization that considers both makespan minimization and workload balance.

## Changes Made

### 1. Enhanced lr_scheduler_train.py

#### New Command Line Arguments
- `--alpha`: Weight for makespan objective (default: 0.5)
- `--beta`: Weight for workload balance objective (default: 0.5)
- `--enable-workload-balance`: Enable multi-objective optimization (flag)

#### Modified Functions
- `fill_demo_data()`: Now accepts alpha and beta parameters for multi-objective training
- Enhanced checkpoint saving to include multi-objective parameters

#### New Features
- Multi-objective configuration logging
- Workload balance integration in training pipeline
- Backward compatibility with single-objective training

### 2. Enhanced utils.py (SchedulingEnv class)

#### New Methods
- `set_multi_objective_params(alpha, beta)`: Configure multi-objective weights
- `calculate_workload_balance()`: Calculate workload balance metric

#### Modified Methods
- `calc_reward_discount()`: Enhanced to include workload balance in reward calculation
- `__init__()`: Added multi-objective parameter initialization

#### New Attributes
- `alpha`: Weight for makespan objective
- `beta`: Weight for workload balance objective
- `enable_workload_balance`: Flag to enable workload balance optimization
- `prev_workload_balance`: Track previous balance for delta calculation

### 3. Integration with multi_objective_utils.py

- Added import of workload balance calculation functions
- Leverages existing multi-objective utilities for consistency

## Usage Examples

### Single-Objective Training (Traditional)
```bash
python lr_scheduler_train.py \
    --path-to-train ./gen/r2t20_001 \
    --num-robots 2 \
    --steps 30000
```

### Multi-Objective Training (Balanced)
```bash
python lr_scheduler_train.py \
    --path-to-train ./gen/r2t20_001 \
    --num-robots 2 \
    --steps 30000 \
    --alpha 0.7 \
    --beta 0.3 \
    --enable-workload-balance
```

### Workload Balance Focused Training
```bash
python lr_scheduler_train.py \
    --path-to-train ./gen/r2t20_001 \
    --num-robots 2 \
    --steps 30000 \
    --alpha 0.3 \
    --beta 0.7 \
    --enable-workload-balance
```

## Technical Details

### Workload Balance Metric
- Calculated as the standard deviation of task counts across robots
- Lower values indicate better balance
- Range: [0, ∞) where 0 = perfectly balanced

### Multi-Objective Reward Function
The reward function combines makespan and workload balance objectives:

```
total_delta = alpha * makespan_delta + beta * balance_weight * balance_delta
reward = -1.0 * total_delta
```

Where:
- `makespan_delta`: Change in makespan objective
- `balance_delta`: Change in workload balance objective
- `balance_weight`: Normalization factor for balance metric

### Checkpoint Enhancement
Checkpoints now include:
- `alpha`: Makespan objective weight
- `beta`: Workload balance objective weight
- `enable_workload_balance`: Multi-objective flag

## Backward Compatibility

The implementation maintains full backward compatibility:
- Default behavior remains single-objective (makespan only)
- Existing scripts and configurations work unchanged
- Multi-objective features are opt-in via command line flags

## Benefits

1. **Improved Task Distribution**: Better workload balance across robots
2. **Flexible Optimization**: Adjustable weights for different priorities
3. **Enhanced Performance**: Consider both efficiency and fairness
4. **Research Capabilities**: Enable multi-objective optimization studies

## Testing

The implementation has been tested with:
- Workload balance calculation accuracy
- Multi-objective parameter handling
- SchedulingEnv integration
- Command line argument parsing
- Backward compatibility verification

## Files Modified

1. `lr_scheduler_train.py`: Main training script with multi-objective support
2. `utils.py`: SchedulingEnv class with workload balance functionality
3. `example_workload_balance_training.py`: Usage examples and demonstrations

## Future Enhancements

Potential areas for future development:
1. Additional objective functions (energy consumption, robot utilization)
2. Pareto front analysis and visualization
3. Adaptive weight adjustment during training
4. Multi-objective evaluation metrics
5. Integration with other training scripts

## Conclusion

The workload balance integration successfully extends `lr_scheduler_train.py` to support multi-objective optimization while maintaining compatibility with existing workflows. This enhancement enables more balanced and fair task allocation in multi-robot scheduling scenarios.
